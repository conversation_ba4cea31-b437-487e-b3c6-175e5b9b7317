<?php
/**
 * Plugin Name:        Wheel-Size API Plugin
 * Plugin URI:         https://wheel-size.com/wordpress
 * Description:        Connects your site with the Wheel-Size.com vehicle fitment API. Enables interactive vehicle search widgets and shortcodes.
 * Version:            1.0.0
 * Author:             Wheel-Size Team
 * Author URI:         https://wheel-size.com
 * License:            GPLv2 or later
 * License URI:        https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain:        wheel-size
 * Domain Path:        /languages
 * Requires PHP:       8.1
 */

declare(strict_types=1);

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Feature flag for Theme Presets functionality
if (!defined('WSF_THEME_PRESETS')) {
    define('WSF_THEME_PRESETS', true);
}

require_once __DIR__ . '/vendor/autoload.php';

use MyTyreFinder\Plugin;

// Plugin activation hook
register_activation_hook(__FILE__, function() {
    // Ensure logs directory exists
    $logDir = __DIR__ . '/logs';
    if (!is_dir($logDir)) {
        mkdir($logDir, 0775, true);
    }
    @chmod($logDir, 0775);

    // Initialize API configuration flag
    // Always reset API configuration on activation for security
    update_option('wheel_size_api_configured', false);

    // SECURITY: Clear any existing API key on activation
    // This ensures fresh start and prevents API key exposure to clients
    delete_option('wheel_size_api_key');

    // Set activation redirect flag for admin notice
    update_option('wheel_size_activation_redirect', true);

    // Set flag to initialize default themes
    update_option('wheel_size_init_themes', true);

    // Create search stats table
    global $wpdb;
    require_once ABSPATH . 'wp-admin/includes/upgrade.php';
    $charset = $wpdb->get_charset_collate();
    $table   = $wpdb->prefix . 'wsf_search_stats';
    $sql = "CREATE TABLE IF NOT EXISTS {$table} (
        id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
        searched_at datetime NOT NULL,
        make varchar(64)   NOT NULL DEFAULT '',
        model varchar(128) NOT NULL DEFAULT '',
        rim tinyint(3) unsigned NOT NULL DEFAULT 0,
        width smallint(5) unsigned NOT NULL DEFAULT 0,
        PRIMARY KEY  (id),
        KEY searched_at (searched_at),
        KEY make (make),
        KEY model (model)
    ) {$charset};";
    dbDelta($sql);
});

// Plugin deactivation hook - reset API configuration
register_deactivation_hook(__FILE__, function() {
    // Reset API configuration flag on deactivation
    // This ensures users must re-validate their API key after reactivation
    update_option('wheel_size_api_configured', false);

    // SECURITY: Clear the API key from database on deactivation
    // This prevents API key from being exposed to clients
    delete_option('wheel_size_api_key');

    // Clear API-related cache
    $cache_keys = [
        'wheel_size_makes',
        'wheel_size_api_test',
        'wheel_size_brand_filters_makes'
    ];

    foreach ($cache_keys as $key) {
        delete_transient($key);
    }

    // Clear any activation redirect flag
    delete_option('wheel_size_activation_redirect');
});

// Initialize plugin after all plugins are loaded.
add_action('plugins_loaded', static function () {
    Plugin::instance()->init();
}); 