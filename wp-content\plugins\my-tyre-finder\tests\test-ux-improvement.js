// Тест UX улучшения - предупреждения о фильтрах
// Запустите в консоли: window.testUXImprovement()

window.testUXImprovement = async function() {
    console.log('🎨 [UX Improvement Test] Тестируем UX улучшения для фильтров...');
    console.log('📋 Цель: Показать понятные сообщения когда фильтры дают пустой результат');
    
    // Функция для AJAX запроса
    async function makeRequest(action, data = {}) {
        const formData = new FormData();
        formData.append('action', action);
        formData.append('nonce', window.WheelFitData?.nonce || 'test');
        
        Object.keys(data).forEach(key => {
            formData.append(key, data[key]);
        });

        const response = await fetch(window.WheelFitData?.ajaxurl || '/wp-admin/admin-ajax.php', {
            method: 'POST',
            body: formData
        });

        return await response.json();
    }
    
    console.log('\n--- Тест 1: Проверка новой структуры ответа ---');
    const result1 = await makeRequest('wf_get_makes');
    console.log('Результат wf_get_makes:', result1);
    
    // Проверяем новую структуру ответа
    const hasNewStructure = result1.success && 
                           typeof result1.data === 'object' && 
                           (Array.isArray(result1.data.data) || Array.isArray(result1.data));
    
    const hasFilterInfo = result1.success && 
                         result1.data && 
                         typeof result1.data.filter_info === 'object';
    
    if (hasNewStructure) {
        console.log('✅ Новая структура ответа работает');
        
        if (hasFilterInfo) {
            console.log('✅ Информация о фильтрах присутствует');
            console.log('Filter info:', result1.data.filter_info);
        } else {
            console.log('⚠️ Информация о фильтрах отсутствует (возможно, фильтры не активны)');
        }
        
        const makes = result1.data.data || result1.data || [];
        console.log(`📊 Количество брендов: ${Array.isArray(makes) ? makes.length : 'неизвестно'}`);
        
    } else {
        console.error('❌ Новая структура ответа не работает');
        console.log('Ожидалась структура: { success: true, data: { data: [...], filter_info: {...} } }');
    }
    
    console.log('\n--- Тест 2: Проверка by year режима ---');
    const result2 = await makeRequest('wf_get_makes_by_year', { year: 2020 });
    console.log('Результат wf_get_makes_by_year:', result2);
    
    const hasNewStructure2 = result2.success && 
                            typeof result2.data === 'object' && 
                            (Array.isArray(result2.data.data) || Array.isArray(result2.data));
    
    const hasFilterInfo2 = result2.success && 
                          result2.data && 
                          typeof result2.data.filter_info === 'object';
    
    if (hasNewStructure2) {
        console.log('✅ Новая структура ответа работает для by year');
        
        if (hasFilterInfo2) {
            console.log('✅ Информация о фильтрах присутствует для by year');
            console.log('Filter info:', result2.data.filter_info);
        } else {
            console.log('⚠️ Информация о фильтрах отсутствует для by year');
        }
        
        const makes2 = result2.data.data || result2.data || [];
        console.log(`📊 Количество брендов для 2020: ${Array.isArray(makes2) ? makes2.length : 'неизвестно'}`);
        
    } else {
        console.error('❌ Новая структура ответа не работает для by year');
    }
    
    console.log('\n--- Тест 3: Проверка JavaScript обработки ---');
    
    // Проверяем что методы добавлены в виджет
    if (window.wheelFitWidget) {
        const widget = window.wheelFitWidget;
        
        if (typeof widget.handleFilterWarning === 'function') {
            console.log('✅ Метод handleFilterWarning добавлен в виджет');
        } else {
            console.error('❌ Метод handleFilterWarning отсутствует в виджете');
        }
        
        if (typeof widget.clearFilterWarnings === 'function') {
            console.log('✅ Метод clearFilterWarnings добавлен в виджет');
        } else {
            console.error('❌ Метод clearFilterWarnings отсутствует в виджете');
        }
        
        if (typeof widget.showFilterToast === 'function') {
            console.log('✅ Метод showFilterToast добавлен в виджет');
        } else {
            console.error('❌ Метод showFilterToast отсутствует в виджете');
        }
        
        // Тестируем показ toast-уведомления
        console.log('\n--- Тест 4: Демонстрация toast-уведомления ---');

        const mockFilterInfo = {
            has_filters: true,
            empty_due_to_filters: true,
            message: 'Selected brands (BMW, Audi) are not available in Southeast Asia.',
            suggestions: [
                'Clear brand filters to see all available brands',
                'Select different brands',
                'Change region settings'
            ]
        };

        try {
            widget.handleFilterWarning(mockFilterInfo, 'make');
            console.log('✅ Демонстрационное toast-уведомление показано');
            console.log('🔍 Проверьте правый верхний угол экрана - должно появиться красное toast-уведомление');
            console.log('📱 Toast не нарушает лейаут формы и автоматически исчезнет через 8 секунд');

            // Показать дополнительное info toast через 3 секунды
            setTimeout(() => {
                widget.showInfoToast('This is an example of info toast notification!', 'info');
                console.log('✅ Демонстрационное info toast показано');
            }, 3000);

        } catch (error) {
            console.error('❌ Ошибка при показе toast-уведомления:', error);
        }
        
    } else {
        console.error('❌ Виджет не найден (window.wheelFitWidget)');
    }
    
    console.log('\n--- Анализ результатов ---');
    
    const allTestsPassed = hasNewStructure && hasNewStructure2 &&
                          window.wheelFitWidget &&
                          typeof window.wheelFitWidget.handleFilterWarning === 'function' &&
                          typeof window.wheelFitWidget.showFilterToast === 'function';
    
    if (allTestsPassed) {
        console.log('🎉 UX УЛУЧШЕНИЕ УСПЕШНО РЕАЛИЗОВАНО!');
        console.log('✅ Новая структура AJAX ответов работает');
        console.log('✅ Информация о фильтрах передается корректно');
        console.log('✅ JavaScript методы для показа toast-уведомлений добавлены');
        console.log('✅ Демонстрационные toast-уведомления работают');

        console.log('\n🎯 Что теперь происходит:');
        console.log('• При пустом результате из-за фильтров показывается toast-уведомление в углу экрана');
        console.log('• Toast НЕ нарушает лейаут формы - все 4 лейаута остаются нетронутыми');
        console.log('• Пользователь видит причину (какие бренды недоступны в каком регионе)');
        console.log('• Предлагаются кнопки для быстрого исправления ситуации');
        console.log('• Toast автоматически исчезает через 8 секунд');
        
    } else {
        console.error('❌ UX УЛУЧШЕНИЕ НЕ ПОЛНОСТЬЮ РАБОТАЕТ');
        console.log('Проблемы:');
        if (!hasNewStructure) console.log('• Новая структура ответа wf_get_makes не работает');
        if (!hasNewStructure2) console.log('• Новая структура ответа wf_get_makes_by_year не работает');
        if (!window.wheelFitWidget) console.log('• Виджет не найден');
        if (window.wheelFitWidget && typeof window.wheelFitWidget.showFilterToast !== 'function') {
            console.log('• JavaScript методы для toast-уведомлений не добавлены');
        }
    }
    
    console.log('\n--- Следующие шаги ---');
    if (allTestsPassed) {
        console.log('1. ✅ Настройте фильтры в админке для создания пустого результата');
        console.log('2. ✅ Протестируйте виджет - должны появляться предупреждения');
        console.log('3. ✅ Проверьте что кнопки "Clear Filters" и "Dismiss" работают');
        console.log('4. ✅ Убедитесь что сообщения понятны пользователям');
    } else {
        console.log('1. 🔧 Очистите кэш и перезагрузите страницу');
        console.log('2. 🔧 Проверьте что изменения в PHP и JS применились');
        console.log('3. 🔧 Запустите тест снова');
    }
    
    return {
        newStructure: hasNewStructure,
        newStructureByYear: hasNewStructure2,
        widgetExists: !!window.wheelFitWidget,
        methodsAdded: window.wheelFitWidget && typeof window.wheelFitWidget.handleFilterWarning === 'function',
        overallSuccess: allTestsPassed,
        result1,
        result2
    };
};

console.log('🎨 [UX Improvement Test] Скрипт загружен.');
console.log('📋 Этот тест проверяет UX улучшения для показа предупреждений о фильтрах.');
console.log('🚀 Запустите: window.testUXImprovement()');
