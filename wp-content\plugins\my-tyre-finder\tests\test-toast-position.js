// Тест позиционирования toast-уведомлений
// Запустите в консоли: window.testToastPosition()

window.testToastPosition = function() {
    console.log('🎯 [Toast Position Test] Тестируем позиционирование toast-уведомлений...');
    
    if (!window.wheelFitWidget) {
        console.error('❌ Виджет не найден (window.wheelFitWidget)');
        return;
    }
    
    const widget = window.wheelFitWidget;
    
    // Проверяем наличие админ-бара
    const adminBar = document.getElementById('wpadminbar');
    const hasAdminBar = !!adminBar;
    const adminBarHeight = hasAdminBar ? adminBar.offsetHeight : 0;
    
    console.log(`📊 Информация о странице:`);
    console.log(`• Админ-бар: ${hasAdminBar ? 'есть' : 'нет'}`);
    console.log(`• Высота админ-бара: ${adminBarHeight}px`);
    console.log(`• Ожидаемый отступ сверху: ${hasAdminBar ? (adminBarHeight + 20) : 60}px`);
    
    // Тест 1: Показать filter toast
    console.log('\n--- Тест 1: Filter Toast ---');
    const mockFilterInfo = {
        has_filters: true,
        empty_due_to_filters: true,
        message: 'Selected brands (BMW, Audi) are not available in Southeast Asia.',
        suggestions: [
            'Clear brand filters to see all available brands',
            'Select different brands',
            'Change region settings'
        ]
    };
    
    try {
        widget.showFilterToast(mockFilterInfo);
        console.log('✅ Filter toast показан');
        
        // Проверяем позицию контейнера
        setTimeout(() => {
            const container = document.getElementById('wsf-toast-container');
            if (container) {
                const rect = container.getBoundingClientRect();
                const computedStyle = window.getComputedStyle(container);
                console.log(`📍 Позиция контейнера:`);
                console.log(`• top: ${computedStyle.top}`);
                console.log(`• right: ${computedStyle.right}`);
                console.log(`• Фактическая позиция: ${rect.top}px от верха экрана`);
                
                if (hasAdminBar && rect.top < adminBarHeight) {
                    console.warn('⚠️ Toast перекрывается с админ-баром!');
                } else {
                    console.log('✅ Позиционирование корректное');
                }
            }
        }, 100);
        
    } catch (error) {
        console.error('❌ Ошибка при показе filter toast:', error);
    }
    
    // Тест 2: Показать info toast через 2 секунды
    setTimeout(() => {
        console.log('\n--- Тест 2: Info Toast ---');
        try {
            widget.showInfoToast('This is a test info message to check positioning!', 'info');
            console.log('✅ Info toast показан');
            
            // Проверяем что нет дублирования
            setTimeout(() => {
                const filterToasts = document.querySelectorAll('.wsf-filter-toast');
                const infoToasts = document.querySelectorAll('.wsf-info-toast');
                console.log(`📊 Количество toast'ов:`);
                console.log(`• Filter toasts: ${filterToasts.length}`);
                console.log(`• Info toasts: ${infoToasts.length}`);
                
                if (filterToasts.length > 1) {
                    console.warn('⚠️ Обнаружено дублирование filter toast!');
                } else {
                    console.log('✅ Дублирования filter toast нет');
                }
                
                if (infoToasts.length > 1) {
                    console.warn('⚠️ Обнаружено дублирование info toast!');
                } else {
                    console.log('✅ Дублирования info toast нет');
                }
            }, 100);
            
        } catch (error) {
            console.error('❌ Ошибка при показе info toast:', error);
        }
    }, 2000);
    
    // Тест 3: Проверить очистку через 5 секунд
    setTimeout(() => {
        console.log('\n--- Тест 3: Очистка Toast ---');
        try {
            widget.clearFilterWarnings();
            console.log('✅ Команда очистки выполнена');
            
            setTimeout(() => {
                const allToasts = document.querySelectorAll('.wsf-filter-toast, .wsf-info-toast');
                console.log(`📊 Оставшихся toast'ов: ${allToasts.length}`);
                
                if (allToasts.length === 0) {
                    console.log('✅ Все toast\'ы успешно очищены');
                } else {
                    console.warn('⚠️ Некоторые toast\'ы не были очищены');
                }
            }, 500);
            
        } catch (error) {
            console.error('❌ Ошибка при очистке toast:', error);
        }
    }, 5000);
    
    // Тест 4: Проверить повторное создание через 7 секунд
    setTimeout(() => {
        console.log('\n--- Тест 4: Повторное создание ---');
        try {
            widget.showFilterToast(mockFilterInfo);
            console.log('✅ Повторный filter toast показан');
            console.log('🔍 Проверьте что позиция корректная и нет дублирования');
            
        } catch (error) {
            console.error('❌ Ошибка при повторном показе:', error);
        }
    }, 7000);
    
    console.log('\n📋 Что проверить:');
    console.log('1. Toast появляется в правом верхнем углу');
    console.log('2. Toast НЕ перекрывается с админ-баром (если есть)');
    console.log('3. Нет дублирования toast\'ов');
    console.log('4. Toast автоматически исчезают');
    console.log('5. Кнопки "Clear Filters" и "Dismiss" работают');
    
    return {
        hasAdminBar,
        adminBarHeight,
        expectedTopOffset: hasAdminBar ? (adminBarHeight + 20) : 60
    };
};

console.log('🎯 [Toast Position Test] Скрипт загружен.');
console.log('📋 Этот тест проверяет корректное позиционирование toast-уведомлений.');
console.log('🚀 Запустите: window.testToastPosition()');
