<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generation Year Range Display Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; }
        select { padding: 5px; margin: 5px; min-width: 200px; }
        .test-result { margin-top: 10px; padding: 10px; border-radius: 3px; }
        .test-result.success { background: #d4edda; border: 1px solid #c3e6cb; }
        .test-result.error { background: #f8d7da; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <h1>Generation Year Range Display Test</h1>
    <p>This test verifies that generation names are displayed correctly using year ranges when name is empty.</p>

    <div class="test-section">
        <h2>Test Cases</h2>
        
        <div class="test-case">
            <h3>Test 1: Generation with empty name but year range</h3>
            <p>Generation data: <code>{ slug: "4d8bd3f735", name: "", start: 2020, end: 2026 }</p>
            <select id="test1-select">
                <option value="">Choose a generation</option>
            </select>
            <div id="test1-result" class="test-result"></div>
        </div>

        <div class="test-case">
            <h3>Test 2: Generation with single year</h3>
            <p>Generation data: <code>{ slug: "abc123", name: "", start: 2023, end: 2023 }</p>
            <select id="test2-select">
                <option value="">Choose a generation</option>
            </select>
            <div id="test2-result" class="test-result"></div>
        </div>

        <div class="test-case">
            <h3>Test 3: Generation with only start year</h3>
            <p>Generation data: <code>{ slug: "def456", name: "", start: 2024, end: null }</p>
            <select id="test3-select">
                <option value="">Choose a generation</option>
            </select>
            <div id="test3-result" class="test-result"></div>
        </div>

        <div class="test-case">
            <h3>Test 4: Generation with only end year</h3>
            <p>Generation data: <code>{ slug: "ghi789", name: "", start: null, end: 2025 }</p>
            <select id="test4-select">
                <option value="">Choose a generation</option>
            </select>
            <div id="test4-result" class="test-result"></div>
        </div>

        <div class="test-case">
            <h3>Test 5: Generation with proper name (should use name)</h3>
            <p>Generation data: <code>{ slug: "jkl012", name: "Facelift", start: 2020, end: 2026 }</p>
            <select id="test5-select">
                <option value="">Choose a generation</option>
            </select>
            <div id="test5-result" class="test-result"></div>
        </div>

        <div class="test-case">
            <h3>Test 6: Generation with no years and no name (fallback to slug)</h3>
            <p>Generation data: <code>{ slug: "mno345", name: "", start: null, end: null }</p>
            <select id="test6-select">
                <option value="">Choose a generation</option>
            </select>
            <div id="test6-result" class="test-result"></div>
        </div>
    </div>

    <script>
        // Helper function to detect internal IDs (long alphanumeric strings)
        const isInternalId = (str) => {
            if (!str || typeof str !== 'string') return false;
            return /^[a-f0-9]{8,}$/i.test(str) || /^[a-z0-9]{10,}$/i.test(str);
        };

        // Helper function to create year range display
        const createYearRangeDisplay = (start, end) => {
            if (start && end) {
                if (start === end) {
                    return `${start}`;
                } else {
                    return `${start}–${end}`;
                }
            } else if (start) {
                return `${start}+`;
            } else if (end) {
                return `–${end}`;
            }
            return null;
        };

        // Function to populate generation select (simplified version of the real logic)
        function populateGenerationSelect(selectId, generation) {
            const select = document.getElementById(selectId);
            if (!select) return;

            // Clear existing options except placeholder
            while (select.options.length > 1) {
                select.remove(1);
            }

            // Find the best display label
            let displayLabel = null;

            // Check fields in priority order, excluding internal IDs
            const candidateFields = ['name', 'title', 'range', 'year_range', 'gen'];
            for (const field of candidateFields) {
                if (generation[field] && !isInternalId(generation[field]) && String(generation[field]).trim().toLowerCase() !== 'generation') {
                    displayLabel = generation[field];
                    break;
                }
            }

            // If no good name found, try to create from year range
            if (!displayLabel && (generation.start || generation.end)) {
                const yearRange = createYearRangeDisplay(generation.start, generation.end);
                if (yearRange) {
                    displayLabel = yearRange;
                }
            }

            // If still no good name, check slug
            if (!displayLabel && generation.slug && !isInternalId(generation.slug)) {
                displayLabel = generation.slug;
            }

            // If still no name, try id
            if (!displayLabel && generation.id && !isInternalId(generation.id)) {
                displayLabel = generation.id;
            }

            // Last fallback
            if (!displayLabel) {
                const anyField = generation.name || generation.title || generation.range || generation.year_range || generation.gen || generation.slug || generation.id;
                if (anyField && String(anyField).trim().toLowerCase() !== 'generation') {
                    displayLabel = anyField;
                } else {
                    displayLabel = 'Unknown Generation';
                }
            }

            // Priority for value: slug > id > name > title > range
            const value = generation.slug || generation.id || generation.name || generation.title || generation.range || displayLabel;

            // Don't allow empty or useless "Generation" placeholder
            if (String(displayLabel).trim().toLowerCase() === 'generation') {
                displayLabel = generation.slug || generation.id || 'Unknown Generation';
            }

            select.add(new Option(displayLabel, value));

            return { displayLabel, value };
        }

        // Test data
        const testCases = [
            {
                id: 'test1',
                data: { slug: "4d8bd3f735", name: "", start: 2020, end: 2026 },
                expected: "2020–2026"
            },
            {
                id: 'test2',
                data: { slug: "abc123", name: "", start: 2023, end: 2023 },
                expected: "2023"
            },
            {
                id: 'test3',
                data: { slug: "def456", name: "", start: 2024, end: null },
                expected: "2024+"
            },
            {
                id: 'test4',
                data: { slug: "ghi789", name: "", start: null, end: 2025 },
                expected: "–2025"
            },
            {
                id: 'test5',
                data: { slug: "jkl012", name: "Facelift", start: 2020, end: 2026 },
                expected: "Facelift"
            },
            {
                id: 'test6',
                data: { slug: "mno345", name: "", start: null, end: null },
                expected: "mno345"
            }
        ];

        // Run tests
        testCases.forEach(testCase => {
            const result = populateGenerationSelect(`${testCase.id}-select`, testCase.data);
            const resultDiv = document.getElementById(`${testCase.id}-result`);
            
            if (result && result.displayLabel === testCase.expected) {
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = `<strong>✅ PASS:</strong> Displayed "${result.displayLabel}" (expected "${testCase.expected}")`;
            } else {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `<strong>❌ FAIL:</strong> Displayed "${result ? result.displayLabel : 'nothing'}" (expected "${testCase.expected}")`;
            }
        });

        console.log('Generation year range display tests completed');
    </script>
</body>
</html> 